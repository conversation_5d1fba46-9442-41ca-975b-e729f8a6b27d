@php
    $courses = App\Models\Course::where('id', '!=', $id)
        ->where('status', 'active')
        ->orderBy('title', 'asc')
        ->get();
@endphp

<div class="ol-card">
    <div class="ol-card-body">
        <form action="{{ route('admin.course.copy_lessons') }}" method="post" id="copy-lessons-form">
            @csrf
            <input type="hidden" name="target_course_id" value="{{ $id }}">

            <div class="mb-3">
                <label for="source_course_id" class="form-label">Chọn khóa học nguồn</label>
                <select class="form-select" name="source_course_id" id="source_course_id" required onchange="loadCourseCurriculum(this.value)">
                    <option value="">-- Ch<PERSON>n kh<PERSON>a học --</option>
                    @foreach($courses as $course)
                        <option value="{{ $course->id }}">{{ $course->title }}</option>
                    @endforeach
                </select>
            </div>

            <div id="course-curriculum-preview" style="display: none;">
                <div class="mb-3">
                    <label class="form-label">Nội dung khóa học</label>
                    <div id="curriculum-content" class="border rounded p-3" style="max-height: 400px; overflow-y: auto;">
                        <!-- Curriculum content will be loaded here -->
                    </div>
                </div>

                <div class="mb-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="copy_all" checked onchange="toggleAllSections(this)">
                        <label class="form-check-label" for="copy_all">
                            <strong>Sao chép tất cả sections và lessons</strong>
                        </label>
                    </div>
                </div>
            </div>

            <div class="d-flex justify-content-end gap-2">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                <button type="submit" class="btn btn-primary" id="copy-btn" disabled>
                    <i class="fi-rr-copy me-1"></i> Sao chép
                </button>
            </div>
        </form>
    </div>
</div>

<script>
function loadCourseCurriculum(courseId) {
    if (!courseId) {
        document.getElementById('course-curriculum-preview').style.display = 'none';
        document.getElementById('copy-btn').disabled = true;
        return;
    }

    // Show loading
    document.getElementById('curriculum-content').innerHTML = '<div class="text-center"><i class="fi-rr-loading"></i> Đang tải...</div>';
    document.getElementById('course-curriculum-preview').style.display = 'block';

    // Fetch curriculum data
    fetch(`{{ url('/admin/course/get-curriculum') }}/${courseId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                renderCurriculum(data.curriculum);
                document.getElementById('copy-btn').disabled = false;
            } else {
                document.getElementById('curriculum-content').innerHTML = '<div class="text-danger">Không thể tải nội dung khóa học</div>';
            }
        })
        .catch(error => {
            console.error('Error:', error);
            document.getElementById('curriculum-content').innerHTML = '<div class="text-danger">Có lỗi xảy ra khi tải dữ liệu</div>';
        });
}

function renderCurriculum(curriculum) {
    let html = '';

    if (curriculum.length === 0) {
        html = '<div class="text-muted">Khóa học này chưa có nội dung</div>';
    } else {
        curriculum.forEach((section, sectionIndex) => {
            html += `
                <div class="section-item mb-3">
                    <div class="form-check mb-2">
                        <input class="form-check-input section-checkbox" type="checkbox"
                               id="section_${section.id}" name="sections[]" value="${section.id}"
                               checked onchange="toggleSectionLessons(${section.id})">
                        <label class="form-check-label fw-bold" for="section_${section.id}">
                            <i class="fi-rr-folder me-1"></i> ${section.title}
                        </label>
                    </div>
                    <div class="lessons-list ms-4" id="lessons_${section.id}">
            `;

            if (section.lessons && section.lessons.length > 0) {
                section.lessons.forEach(lesson => {
                    html += `
                        <div class="form-check mb-1">
                            <input class="form-check-input lesson-checkbox lesson-${section.id}"
                                   type="checkbox" id="lesson_${lesson.id}"
                                   name="lessons[]" value="${lesson.id}" checked>
                            <label class="form-check-label" for="lesson_${lesson.id}">
                                <i class="fi-rr-document me-1"></i> ${lesson.title}
                                <small class="text-muted">(${lesson.lesson_type})</small>
                            </label>
                        </div>
                    `;
                });
            } else {
                html += '<div class="text-muted small">Không có bài học</div>';
            }

            html += `
                    </div>
                </div>
            `;
        });
    }

    document.getElementById('curriculum-content').innerHTML = html;
}

function toggleAllSections(checkbox) {
    const sectionCheckboxes = document.querySelectorAll('.section-checkbox');
    const lessonCheckboxes = document.querySelectorAll('.lesson-checkbox');

    sectionCheckboxes.forEach(cb => cb.checked = checkbox.checked);
    lessonCheckboxes.forEach(cb => cb.checked = checkbox.checked);
}

function toggleSectionLessons(sectionId) {
    const sectionCheckbox = document.getElementById(`section_${sectionId}`);
    const lessonCheckboxes = document.querySelectorAll(`.lesson-${sectionId}`);

    lessonCheckboxes.forEach(cb => cb.checked = sectionCheckbox.checked);
}

// Handle form submission
document.getElementById('copy-lessons-form').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const copyBtn = document.getElementById('copy-btn');

    // Show loading state
    copyBtn.innerHTML = '<i class="fi-rr-loading me-1"></i> Đang sao chép...';
    copyBtn.disabled = true;

    fetch(this.action, {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Close modal and reload page
            bootstrap.Modal.getInstance(document.querySelector('.modal')).hide();
            location.reload();
        } else {
            alert(data.message || 'Có lỗi xảy ra');
            copyBtn.innerHTML = '<i class="fi-rr-copy me-1"></i> Sao chép';
            copyBtn.disabled = false;
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Có lỗi xảy ra khi sao chép');
        copyBtn.innerHTML = '<i class="fi-rr-copy me-1"></i> Sao chép';
        copyBtn.disabled = false;
    });
});
</script>
